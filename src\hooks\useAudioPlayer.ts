'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface UseAudioPlayerProps {
  initialSrc: string;
}

interface AudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  hasStarted: boolean;
  remainingTime: number;
  play: () => void;
  pause: () => void;
  changeSource: (newSrc: string, playWhenReady?: boolean) => void;
  seek: (time: number, duration?: number) => void;
  setVolume: (volume: number) => void;
  reset: () => void;
}

export const useAudioPlayer = ({ initialSrc }: UseAudioPlayerProps): AudioPlayerState => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolumeState] = useState(1);
  const [hasStarted, setHasStarted] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);

  useEffect(() => {
    if (typeof window !== 'undefined' && !audioRef.current) {
      audioRef.current = new Audio(initialSrc);

      const setAudioData = () => {
        setDuration(audioRef.current?.duration || 0);
        setCurrentTime(audioRef.current?.currentTime || 0);
      };

      const setAudioTime = () => setCurrentTime(audioRef.current?.currentTime || 0);
      const handlePlay = () => setIsPlaying(true);
      const handlePause = () => setIsPlaying(false);

      audioRef.current.addEventListener('loadeddata', setAudioData);
      audioRef.current.addEventListener('timeupdate', setAudioTime);
      audioRef.current.addEventListener('play', handlePlay);
      audioRef.current.addEventListener('pause', handlePause);

      // cleanup
      return () => {
        audioRef.current?.removeEventListener('loadeddata', setAudioData);
        audioRef.current?.removeEventListener('timeupdate', setAudioTime);
        audioRef.current?.removeEventListener('play', handlePlay);
        audioRef.current?.removeEventListener('pause', handlePause);
        audioRef.current?.pause();
        audioRef.current = null;
      };
    }
  }, [initialSrc]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  // Calculate remaining time whenever currentTime or duration changes
  useEffect(() => {
    if (duration > 0) {
      const remaining = Math.max(0, duration - currentTime);
      setRemainingTime(remaining);
    }
  }, [currentTime, duration]);

  const play = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.play();
      // Mark as started once play is called for the first time
      if (!hasStarted) {
        setHasStarted(true);
      }
    }
  }, [hasStarted]);

  const pause = useCallback(() => {
    // In one-shot mode, pause is disabled after playback has started
    if (!hasStarted && audioRef.current) {
      audioRef.current.pause();
    }
  }, [hasStarted]);

  const changeSource = useCallback((newSrc: string, playWhenReady = true) => {
    if (audioRef.current) {
      const time = audioRef.current.currentTime;
      const wasPlaying = !audioRef.current.paused;

      console.log(`[AudioPlayer] Starting source change: ${audioRef.current.src} -> ${newSrc}`);
      console.log(`[AudioPlayer] Current state - time: ${time}s, playing: ${!audioRef.current.paused}`);

      // Complete stop and cleanup
      audioRef.current.pause();
      audioRef.current.currentTime = 0;

      // Wait a brief moment to ensure audio stops completely
      setTimeout(() => {
        if (audioRef.current) {
          // Add cache-busting parameter to ensure fresh load
          const cacheBustingSrc = newSrc + (newSrc.includes('?') ? '&' : '?') + 't=' + Date.now();

          console.log(`[AudioPlayer] Setting new source: ${cacheBustingSrc}`);
          audioRef.current.src = cacheBustingSrc;
          audioRef.current.load();

          const onCanPlay = () => {
            if (audioRef.current) {
              console.log(`[AudioPlayer] New audio loaded, seeking to ${time}s`);
              audioRef.current.currentTime = time;

              if (playWhenReady && wasPlaying) {
                console.log(`[AudioPlayer] Resuming playback`);
                audioRef.current.play().catch((error) => {
                  console.error('[AudioPlayer] Failed to play after source change:', error);
                });
              }
            }
            audioRef.current?.removeEventListener('canplay', onCanPlay);
          };

          const onError = () => {
            console.error('[AudioPlayer] Failed to load audio source:', newSrc);
            audioRef.current?.removeEventListener('error', onError);
            audioRef.current?.removeEventListener('canplay', onCanPlay);
          };

          audioRef.current.addEventListener('canplay', onCanPlay);
          audioRef.current.addEventListener('error', onError);
        }
      }, 50); // Small delay to ensure complete stop
    }
  }, []);

  const seek = useCallback((time: number, newDuration?: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
      if (newDuration !== undefined) {
        setDuration(newDuration);
      }
    }
  }, []);

  const setVolume = useCallback((newVolume: number) => {
    if (audioRef.current) {
      const clampedVolume = Math.max(0, Math.min(1, newVolume));
      audioRef.current.volume = clampedVolume;
      setVolumeState(clampedVolume);
    }
  }, []);

  const reset = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setHasStarted(false);
    setRemainingTime(0);
  }, []);

  return {
    isPlaying,
    currentTime,
    duration,
    volume,
    hasStarted,
    remainingTime,
    play,
    pause,
    changeSource,
    seek,
    setVolume,
    reset
  };
};

