'use client';

import React, { useState, useEffect } from 'react';
import { useDualTrackAudioPlayer } from '@/hooks/useDualTrackAudioPlayer';
import { Button } from '@/components/ui/button';
import { Play, Pause, SkipForward } from 'lucide-react';

const AUDIO_UTOPIA = '/audio/Timeline_Utopia.mp3';
const AUDIO_DYSTOPIA = '/audio/Timeline_Dystopia.mp3';

const TestSeamlessAudioPage: React.FC = () => {
  const [testLog, setTestLog] = useState<string[]>([]);
  const [isPreloadingStarted, setIsPreloadingStarted] = useState(false);

  const {
    isPlaying,
    currentTime,
    duration,
    volume,
    hasStarted,
    remainingTime,
    activeTrack,
    isPreloading,
    preloadProgress,
    play,
    pause,
    switchToTrack,
    preloadTracks,
    seek,
    setVolume,
    cleanup
  } = useDualTrackAudioPlayer({
    initialSrc: AUDIO_DYSTOPIA,
    utopiaTrack: AUDIO_UTOPIA,
    dystopiaTrack: AUDIO_DYSTOPIA,
  });

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setTestLog(prev => [...prev.slice(-15), logEntry]); // Keep last 15 entries
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleStartPreloading = async () => {
    if (isPreloadingStarted) return;
    
    setIsPreloadingStarted(true);
    addLog('Starting track preloading...');
    
    try {
      await preloadTracks(AUDIO_UTOPIA, AUDIO_DYSTOPIA);
      addLog('Track preloading completed successfully');
    } catch (error) {
      addLog(`Track preloading failed: ${error}`);
    }
  };

  const handleSeekToChoiceTime = () => {
    const choiceTime = 480; // 8:00 mark
    seek(choiceTime);
    addLog(`Seeked to choice time: ${formatTime(choiceTime)}`);
  };

  const handleSwitchToUtopia = () => {
    if (!preloadProgress.utopia) {
      addLog('ERROR: Utopia track not preloaded');
      return;
    }
    
    const beforeTime = currentTime;
    addLog(`Switching to Utopia at ${formatTime(beforeTime)}`);
    switchToTrack('utopia');
    
    setTimeout(() => {
      const afterTime = currentTime;
      addLog(`Switch completed. Time drift: ${Math.abs(afterTime - beforeTime).toFixed(2)}s`);
    }, 100);
  };

  const handleSwitchToDystopia = () => {
    if (!preloadProgress.dystopia) {
      addLog('ERROR: Dystopia track not preloaded');
      return;
    }
    
    const beforeTime = currentTime;
    addLog(`Switching to Dystopia at ${formatTime(beforeTime)}`);
    switchToTrack('dystopia');
    
    setTimeout(() => {
      const afterTime = currentTime;
      addLog(`Switch completed. Time drift: ${Math.abs(afterTime - beforeTime).toFixed(2)}s`);
    }, 100);
  };

  const handleCleanup = () => {
    addLog('Cleaning up preloaded tracks');
    cleanup();
    setIsPreloadingStarted(false);
  };

  useEffect(() => {
    addLog('Dual-track audio player initialized');
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8">
      <div className="max-w-6xl mx-auto text-white">
        <h1 className="text-4xl font-bold mb-8 text-center">Seamless Audio Switching Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Audio Controls */}
          <div className="bg-black/30 rounded-xl p-6 backdrop-blur-lg border border-white/10">
            <h2 className="text-2xl font-semibold mb-4">Audio Controls</h2>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div><strong>Current Time:</strong> {formatTime(currentTime)}</div>
                <div><strong>Duration:</strong> {formatTime(duration)}</div>
                <div><strong>Playing:</strong> {isPlaying ? 'Yes' : 'No'}</div>
                <div><strong>Started:</strong> {hasStarted ? 'Yes' : 'No'}</div>
              </div>

              <div className="space-y-2">
                <div><strong>Active Track:</strong></div>
                <div className="text-sm text-gray-300 break-all">
                  {activeTrack.split('/').pop()}
                </div>
              </div>

              <div className="space-y-2">
                {!hasStarted ? (
                  <Button onClick={play} className="w-full bg-green-600 hover:bg-green-700">
                    <Play className="mr-2 h-4 w-4" />
                    Start Audio
                  </Button>
                ) : (
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      onClick={isPlaying ? pause : play} 
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isPlaying ? <Pause className="mr-2 h-4 w-4" /> : <Play className="mr-2 h-4 w-4" />}
                      {isPlaying ? 'Pause' : 'Play'}
                    </Button>
                    <Button 
                      onClick={handleSeekToChoiceTime}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <SkipForward className="mr-2 h-4 w-4" />
                      Seek to 8:00
                    </Button>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium">Volume: {Math.round(volume * 100)}%</label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volume}
                  onChange={(e) => setVolume(parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Preloading & Switching Controls */}
          <div className="bg-black/30 rounded-xl p-6 backdrop-blur-lg border border-white/10">
            <h2 className="text-2xl font-semibold mb-4">Seamless Switching</h2>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div><strong>Preloading:</strong> {isPreloading ? 'Yes' : 'No'}</div>
                <div><strong>Started:</strong> {isPreloadingStarted ? 'Yes' : 'No'}</div>
                <div><strong>Utopia Ready:</strong> {preloadProgress.utopia ? '✅' : '❌'}</div>
                <div><strong>Dystopia Ready:</strong> {preloadProgress.dystopia ? '✅' : '❌'}</div>
              </div>

              <div className="space-y-2">
                <Button 
                  onClick={handleStartPreloading}
                  disabled={isPreloadingStarted || isPreloading}
                  className="w-full bg-orange-600 hover:bg-orange-700 disabled:opacity-50"
                >
                  {isPreloading ? 'Preloading...' : 'Start Preloading'}
                </Button>

                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    onClick={handleSwitchToUtopia}
                    disabled={!preloadProgress.utopia}
                    className="bg-sky-600 hover:bg-sky-700 disabled:opacity-50"
                  >
                    Switch to Utopia
                  </Button>
                  <Button 
                    onClick={handleSwitchToDystopia}
                    disabled={!preloadProgress.dystopia}
                    className="bg-red-600 hover:bg-red-700 disabled:opacity-50"
                  >
                    Switch to Dystopia
                  </Button>
                </div>

                <Button 
                  onClick={handleCleanup}
                  className="w-full bg-gray-600 hover:bg-gray-700"
                >
                  Cleanup Preloaded Tracks
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Test Log */}
        <div className="mt-8 bg-black/30 rounded-xl p-6 backdrop-blur-lg border border-white/10">
          <h2 className="text-2xl font-semibold mb-4">Test Log</h2>
          <div className="bg-black/50 rounded-lg p-4 h-64 overflow-y-auto">
            <div className="space-y-1 text-sm font-mono">
              {testLog.map((log, index) => (
                <div key={index} className="text-green-400">
                  {log}
                </div>
              ))}
              {testLog.length === 0 && (
                <div className="text-gray-500">No logs yet...</div>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-900/30 rounded-xl p-6 backdrop-blur-lg border border-blue-400/30">
          <h2 className="text-2xl font-semibold mb-4 text-blue-300">Test Instructions</h2>
          <ol className="space-y-2 text-blue-100">
            <li><strong>1.</strong> Click "Start Audio" to begin playback</li>
            <li><strong>2.</strong> Click "Start Preloading" to preload both tracks</li>
            <li><strong>3.</strong> Wait for both tracks to show ✅ (ready)</li>
            <li><strong>4.</strong> Use "Switch to Utopia/Dystopia" buttons to test seamless switching</li>
            <li><strong>5.</strong> Monitor the test log for timing accuracy and any errors</li>
            <li><strong>6.</strong> Verify that switching happens without audio interruption or timeline jumping</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default TestSeamlessAudioPage;
