'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Button } from './ui/button';

interface ChoiceCorridorProps {
  onSelectUtopia: () => void;
  onSelectDystopia: () => void;
  isProcessing?: boolean;
}

const ChoiceCorridor: React.FC<ChoiceCorridorProps> = ({ onSelectUtopia, onSelectDystopia, isProcessing = false }) => {
  const modalVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
  };

  return (
    <motion.div
      data-testid="choice-corridor"
      className="absolute inset-0 flex items-center justify-center z-10"
      variants={modalVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      transition={{ duration: 0.8, ease: 'easeInOut', delay: 3 }}
    >
      <div className="text-center p-8 bg-black/50 rounded-2xl backdrop-blur-lg border border-white/10 shadow-lg">
        <h2 className="text-3xl font-bold text-white mb-4">The Path Diverges</h2>
        <p className="text-lg text-gray-300 mb-8">Your choice will shape the future.</p>
        <div className="flex justify-center gap-x-6">
          <Button
            onClick={onSelectUtopia}
            disabled={isProcessing}
            size="lg"
            className="bg-white/5 border border-white/10 text-white rounded-[6px] px-8 py-3 text-lg font-medium backdrop-blur-sm hover:bg-white/10 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-white/50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? 'Processing...' : 'Choose Utopia'}
          </Button>
          <Button
            onClick={onSelectDystopia}
            disabled={isProcessing}
            size="lg"
            className="bg-white/5 border border-white/10 text-white rounded-[6px] px-8 py-3 text-lg font-medium backdrop-blur-sm hover:bg-white/10 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-white/50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? 'Processing...' : 'Choose Dystopia'}
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

export default ChoiceCorridor;
