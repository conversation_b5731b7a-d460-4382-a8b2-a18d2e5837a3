'use client';

import React, { useMemo } from 'react';
import { random } from '@/lib/random';
import styles from './IndustrialPulseShapes.module.css';

interface IndustrialPulseShapesProps {
  progress: number;
}

const IndustrialPulseShapes: React.FC<IndustrialPulseShapesProps> = ({ progress }) => {
  const particleCount = 40; // A low number of particles for high performance

  const particles = useMemo(() => {
    return Array.from({ length: particleCount }).map((_, i) => {
      const size = random(15, 50);
      const duration = random(15, 25);
      const delay = random(0, 20);
      const x = random(10, 90); // position in vw

      return {
        id: i,
        style: {
          width: `${size}px`,
          height: `${size}px`,
          left: `${x}vw`,
          animationDuration: `${duration}s`,
          animationDelay: `${delay}s`,
        },
      };
    });
  }, []); // Empty dependency array ensures this runs only once

  // Only render a fraction of particles based on the era's progress
  const visibleParticles = useMemo(() => {
    const count = Math.floor(progress * particles.length);
    return particles.slice(0, count);
  }, [progress, particles]);

  return (
    <div className={styles.container}>
      {visibleParticles.map(particle => (
        <div
          key={particle.id}
          className={styles.particle}
          style={particle.style as React.CSSProperties}
        />
      ))}
    </div>
  );
};

export default IndustrialPulseShapes;
