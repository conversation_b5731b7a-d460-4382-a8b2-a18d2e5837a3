import React from 'react';
import { render } from '@testing-library/react';
import UtopiaShapes from '@/components/animations/UtopiaShapes';

jest.mock('@/components/animations/UtopiaShapes.module.css', () => ({
  utopiaContainer: 'utopiaContainer',
  sun: 'sun',
  structure: 'structure',
  structure1: 'structure1',
  structure2: 'structure2',
  structure3: 'structure3',
  vines: 'vines',
  vinePath1: 'vinePath1',
  vinePath2: 'vinePath2',
  vinePath3: 'vinePath3',
  particles: 'particles',
  particle: 'particle',
}));

describe('UtopiaShapes', () => {
  it('renders correctly and matches snapshot', () => {
    const { container } = render(<UtopiaShapes />);
    expect(container).toMatchSnapshot();
  });

  it('renders the main container', () => {
    const { container } = render(<UtopiaShapes />);
    expect(container.querySelector('.utopiaContainer')).toBeInTheDocument();
  });

  it('renders the sun element', () => {
    const { container } = render(<UtopiaShapes />);
    expect(container.querySelector('.sun')).toBeInTheDocument();
  });

  it('renders all three structures', () => {
    const { container } = render(<UtopiaShapes />);
    expect(container.querySelectorAll('.structure').length).toBe(3);
  });

  it('renders the svg for vines with three paths', () => {
    const { container } = render(<UtopiaShapes />);
    const svg = container.querySelector('.vines');
    expect(svg).toBeInTheDocument();
    expect(svg?.querySelectorAll('path').length).toBe(3);
  });

  it('renders the correct number of particles', () => {
    const { container } = render(<UtopiaShapes />);
    expect(container.querySelectorAll('.particle').length).toBe(30);
  });
});
