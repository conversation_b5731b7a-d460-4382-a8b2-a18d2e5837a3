import { NextResponse } from 'next/server';
import { isProductionEnvironment, incrementMockVote } from '@/lib/mockVoteStorage';

export async function POST(request: Request) {
  try {
    const { choice } = await request.json();

    if (choice !== 'utopia' && choice !== 'dystopia') {
      return NextResponse.json({ message: 'Invalid choice.' }, { status: 400 });
    }

    if (isProductionEnvironment()) {
      // Use Vercel KV in production
      const { kv } = await import('@vercel/kv');
      const key = `soundscape:votes:${choice}`;
      await kv.incr(key);
    } else {
      // Use mock storage in development
      incrementMockVote(choice);
    }

    console.log(`Vote recorded for: ${choice}`);

    return NextResponse.json({ message: `Vote for ${choice} recorded successfully.` }, { status: 200 });
  } catch (error) {
    console.error('Error processing vote:', error);
    return NextResponse.json({ message: 'An error occurred while processing the vote.' }, { status: 500 });
  }
}
