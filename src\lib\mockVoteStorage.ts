// Shared mock storage for local development
// This ensures consistency between vote submission and vote retrieval APIs

interface MockVoteStorage {
  utopia: number;
  dystopia: number;
}

// Global storage that persists across API calls
const mockVotes: MockVoteStorage = {
  utopia: 0,
  dystopia: 0
};

export const getMockVotes = (): MockVoteStorage => {
  return { ...mockVotes };
};

export const incrementMockVote = (choice: 'utopia' | 'dystopia'): void => {
  mockVotes[choice]++;
  console.log(`[DEV MODE] Mock vote recorded for: ${choice}`, mockVotes);
};

export const resetMockVotes = (): void => {
  mockVotes.utopia = 0;
  mockVotes.dystopia = 0;
  console.log('[DEV MODE] Mock votes reset');
};

export const isProductionEnvironment = (): boolean => {
  return !!(process.env.KV_URL && process.env.KV_REST_API_URL);
};
