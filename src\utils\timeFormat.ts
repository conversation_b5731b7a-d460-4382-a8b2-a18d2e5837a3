/**
 * Formats time in seconds to a human-readable countdown format
 * @param seconds - Time in seconds
 * @returns Formatted time string (e.g., "542s remaining" or "9:02 remaining")
 */
export const formatCountdown = (seconds: number): string => {
  const totalSeconds = Math.ceil(seconds);
  
  if (totalSeconds <= 0) {
    return "0:00";
  }

  // For times 60 seconds and above, show minutes:seconds format
  const minutes = Math.floor(totalSeconds / 60);
  const remainingSeconds = totalSeconds % 60;

  // Format seconds with leading zero if needed
  const formattedSeconds = remainingSeconds.toString().padStart(2, '0');

  return `${minutes}:${formattedSeconds}`;
};

/**
 * Formats time in seconds to a simple time display format
 * @param seconds - Time in seconds
 * @returns Formatted time string (e.g., "9:02" or "0:42")
 */
export const formatTime = (seconds: number): string => {
  const totalSeconds = Math.floor(seconds);
  const minutes = Math.floor(totalSeconds / 60);
  const remainingSeconds = totalSeconds % 60;
  
  // Format seconds with leading zero if needed
  const formattedSeconds = remainingSeconds.toString().padStart(2, '0');
  
  return `${minutes}:${formattedSeconds}`;
};

/**
 * Formats duration for display purposes
 * @param seconds - Duration in seconds
 * @returns Formatted duration string
 */
export const formatDuration = (seconds: number): string => {
  if (seconds <= 0) {
    return "0:00";
  }
  
  return formatTime(seconds);
};
