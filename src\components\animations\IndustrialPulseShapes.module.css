.container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  bottom: -40px; /* Start from below the screen */
  background-color: rgba(180, 180, 180, 0.5);
  border-radius: 50%;
  will-change: transform, opacity;
  animation-name: rise;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  filter: blur(5px);
}

@keyframes rise {
  from {
    transform: translateY(0) scale(0.5);
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  to {
    transform: translateY(-100vh) scale(1.2);
    opacity: 0;
  }
}
