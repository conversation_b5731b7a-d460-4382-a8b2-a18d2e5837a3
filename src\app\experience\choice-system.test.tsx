import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ExperiencePage from './page';

// Mock the global fetch function for vote submission
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ message: 'Vote recorded successfully' }),
  })
) as jest.Mock;

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn().mockReturnValue(null),
  }),
}));

// Mock the dual track audio player hook
const mockSwitchToTrack = jest.fn().mockResolvedValue(true);
const mockChangeSource = jest.fn();
const mockPlay = jest.fn();
const mockSeek = jest.fn();
const mockCleanup = jest.fn();
const mockPreloadTracks = jest.fn().mockResolvedValue(undefined);

// Create a mock that can be updated per test
let mockCurrentTime = 490; // Within choice window by default

jest.mock('@/hooks/useDualTrackAudioPlayer', () => ({
  useDualTrackAudioPlayer: () => ({
    isPlaying: true,
    currentTime: mockCurrentTime,
    duration: 600,
    volume: 1,
    hasStarted: true,
    remainingTime: 110,
    activeTrack: '/audio/Timeline_Dystopia.mp3',
    isPreloading: false,
    preloadProgress: { utopia: true, dystopia: true },
    setVolume: jest.fn(),
    play: mockPlay,
    changeSource: mockChangeSource,
    switchToTrack: mockSwitchToTrack,
    preloadTracks: mockPreloadTracks,
    seek: mockSeek,
    cleanup: mockCleanup,
  }),
}));

describe('Choice System Integration', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    mockCurrentTime = 490; // Reset to choice window time
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ message: 'Vote recorded successfully' }),
    });
  });

  it('should display choice corridor during choice window', () => {
    render(<ExperiencePage />);

    expect(screen.getByTestId('choice-corridor')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /choose utopia/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /choose dystopia/i })).toBeInTheDocument();
  });

  it('should handle utopia choice with seamless audio switching', async () => {
    const { rerender } = render(<ExperiencePage />);

    const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });
    fireEvent.click(utopiaButton);

    // Wait for async operations to complete
    await waitFor(() => {
      expect(mockSwitchToTrack).toHaveBeenCalledWith('utopia');
    });

    // Verify vote submission was attempted
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/vote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ choice: 'utopia' }),
      });
    });

    // Re-render to trigger state update and check choice corridor disappears
    rerender(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
  });

  it('should handle dystopia choice with seamless audio switching', async () => {
    const { rerender } = render(<ExperiencePage />);

    const dystopiaButton = screen.getByRole('button', { name: /choose dystopia/i });
    fireEvent.click(dystopiaButton);

    // Wait for async operations to complete
    await waitFor(() => {
      expect(mockSwitchToTrack).toHaveBeenCalledWith('dystopia');
    });

    // Verify vote submission was attempted
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/vote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ choice: 'dystopia' }),
      });
    });

    // Re-render to trigger state update and check choice corridor disappears
    rerender(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
  });

  it('should fallback to traditional audio switching when seamless switching fails', async () => {
    // Mock seamless switching to fail
    mockSwitchToTrack.mockResolvedValueOnce(false);

    const { rerender } = render(<ExperiencePage />);

    const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });
    fireEvent.click(utopiaButton);

    // Wait for async operations to complete
    await waitFor(() => {
      expect(mockSwitchToTrack).toHaveBeenCalledWith('utopia');
    });

    // Should fallback to traditional method
    await waitFor(() => {
      expect(mockChangeSource).toHaveBeenCalledWith('/audio/Timeline_Utopia.mp3');
    });

    // Re-render to check choice corridor disappears
    rerender(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
  });

  it('should handle vote submission failures gracefully', async () => {
    // Mock fetch to fail
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    const { rerender } = render(<ExperiencePage />);

    const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });
    fireEvent.click(utopiaButton);

    // Wait for vote submission to fail
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Vote submission failed (non-blocking)'),
        expect.any(Error)
      );
    });

    // Choice should still work despite vote failure
    rerender(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();

    consoleSpy.mockRestore();
  });

  it('should prevent multiple choice selections', async () => {
    const { rerender } = render(<ExperiencePage />);

    const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });
    const dystopiaButton = screen.getByRole('button', { name: /choose dystopia/i });

    // Click utopia first
    fireEvent.click(utopiaButton);

    // Try to click dystopia immediately after
    fireEvent.click(dystopiaButton);

    // Wait for async operations
    await waitFor(() => {
      expect(mockSwitchToTrack).toHaveBeenCalledTimes(1);
    });

    // Should only have been called with utopia
    expect(mockSwitchToTrack).toHaveBeenCalledWith('utopia');
    expect(mockSwitchToTrack).not.toHaveBeenCalledWith('dystopia');

    // Choice corridor should disappear
    rerender(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
  });

  it('should handle audio switching errors gracefully', async () => {
    // Mock both seamless and traditional switching to fail
    mockSwitchToTrack.mockRejectedValueOnce(new Error('Audio error'));
    mockChangeSource.mockImplementationOnce(() => {
      throw new Error('Traditional switching failed');
    });

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    const { rerender } = render(<ExperiencePage />);

    const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });
    fireEvent.click(utopiaButton);

    // Wait for error handling
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Failed to change audio source to utopia'),
        expect.any(Error)
      );
    });

    // Choice corridor should still disappear
    rerender(<ExperiencePage />);
    expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();

    consoleSpy.mockRestore();
  });
});
