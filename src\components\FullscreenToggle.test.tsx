import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import FullscreenToggle from './FullscreenToggle';

// Mock the fullscreen API
const mockRequestFullscreen = jest.fn();
const mockExitFullscreen = jest.fn();

// Mock document properties
Object.defineProperty(document, 'fullscreenEnabled', {
  writable: true,
  value: true,
});

Object.defineProperty(document, 'fullscreenElement', {
  writable: true,
  value: null,
});

Object.defineProperty(document.documentElement, 'requestFullscreen', {
  writable: true,
  value: mockRequestFullscreen,
});

Object.defineProperty(document, 'exitFullscreen', {
  writable: true,
  value: mockExitFullscreen,
});

// Mock addEventListener and removeEventListener
const originalAddEventListener = document.addEventListener;
const originalRemoveEventListener = document.removeEventListener;

describe('FullscreenToggle', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Reset fullscreen state and ensure it's supported
    Object.defineProperty(document, 'fullscreenEnabled', {
      writable: true,
      value: true,
    });

    Object.defineProperty(document, 'fullscreenElement', {
      writable: true,
      value: null,
    });

    // Mock event listeners
    document.addEventListener = jest.fn();
    document.removeEventListener = jest.fn();
  });

  afterEach(() => {
    document.addEventListener = originalAddEventListener;
    document.removeEventListener = originalRemoveEventListener;
  });

  it('renders fullscreen toggle button when supported', () => {
    render(<FullscreenToggle />);

    const button = screen.getByRole('button', { name: /enter fullscreen/i });
    expect(button).toBeInTheDocument();
  });

  it('does not render when fullscreen is not supported', () => {
    // Mock unsupported browser
    Object.defineProperty(document, 'fullscreenEnabled', {
      writable: true,
      value: false,
    });

    const { container } = render(<FullscreenToggle />);
    expect(container.firstChild).toBeNull();
  });

  it('shows maximize icon when not in fullscreen', () => {
    render(<FullscreenToggle />);

    const button = screen.getByRole('button', { name: /enter fullscreen/i });
    expect(button).toBeInTheDocument();

    // Check for maximize icon (we can't easily test the icon directly, but we can test the aria-label)
    expect(button).toHaveAttribute('aria-label', 'Enter fullscreen');
  });

  it('shows minimize icon when in fullscreen', () => {
    // Mock fullscreen state
    Object.defineProperty(document, 'fullscreenElement', {
      writable: true,
      value: document.documentElement,
    });

    render(<FullscreenToggle />);

    const button = screen.getByRole('button', { name: /exit fullscreen/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('aria-label', 'Exit fullscreen');
  });

  it('calls requestFullscreen when clicking to enter fullscreen', async () => {
    mockRequestFullscreen.mockResolvedValue(undefined);

    render(<FullscreenToggle />);

    const button = screen.getByRole('button', { name: /enter fullscreen/i });
    fireEvent.click(button);

    await waitFor(() => {
      expect(mockRequestFullscreen).toHaveBeenCalledTimes(1);
    });
  });

  it('calls exitFullscreen when clicking to exit fullscreen', async () => {
    // Mock fullscreen state
    Object.defineProperty(document, 'fullscreenElement', {
      writable: true,
      value: document.documentElement,
    });

    mockExitFullscreen.mockResolvedValue(undefined);

    render(<FullscreenToggle />);

    const button = screen.getByRole('button', { name: /exit fullscreen/i });
    fireEvent.click(button);

    await waitFor(() => {
      expect(mockExitFullscreen).toHaveBeenCalledTimes(1);
    });
  });

  it('handles fullscreen API errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    mockRequestFullscreen.mockRejectedValue(new Error('Fullscreen failed'));

    render(<FullscreenToggle />);

    const button = screen.getByRole('button', { name: /enter fullscreen/i });
    fireEvent.click(button);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error entering fullscreen:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('sets up fullscreen change event listeners', () => {
    render(<FullscreenToggle />);

    expect(document.addEventListener).toHaveBeenCalledWith('fullscreenchange', expect.any(Function));
    expect(document.addEventListener).toHaveBeenCalledWith('webkitfullscreenchange', expect.any(Function));
    expect(document.addEventListener).toHaveBeenCalledWith('mozfullscreenchange', expect.any(Function));
    expect(document.addEventListener).toHaveBeenCalledWith('MSFullscreenChange', expect.any(Function));
  });

  it('cleans up event listeners on unmount', () => {
    const { unmount } = render(<FullscreenToggle />);

    unmount();

    expect(document.removeEventListener).toHaveBeenCalledWith('fullscreenchange', expect.any(Function));
    expect(document.removeEventListener).toHaveBeenCalledWith('webkitfullscreenchange', expect.any(Function));
    expect(document.removeEventListener).toHaveBeenCalledWith('mozfullscreenchange', expect.any(Function));
    expect(document.removeEventListener).toHaveBeenCalledWith('MSFullscreenChange', expect.any(Function));
  });

  it('applies custom className when provided', () => {
    render(<FullscreenToggle className="custom-class" />);

    const container = screen.getByRole('button').parentElement;
    expect(container).toHaveClass('custom-class');
  });

  it('has proper accessibility attributes', () => {
    render(<FullscreenToggle />);

    const button = screen.getByRole('button', { name: /enter fullscreen/i });
    expect(button).toHaveAttribute('aria-label', 'Enter fullscreen');
    expect(button).toHaveAttribute('title', 'Enter fullscreen');
  });
});
