'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import rawTimedTextData from '@/data/timedText.json';
import { Section, UserChoice, TimedTextData } from '@/types';

// Cast the imported JSON to our specific TypeScript type
const timedTextData = rawTimedTextData as TimedTextData;

interface TimedTextDisplayProps {
  currentTime: number;
  currentSection: Section | null;
  userChoice: UserChoice;
  isPlaying: boolean;
}

const TimedTextDisplay: React.FC<TimedTextDisplayProps> = ({
  currentTime,
  currentSection,
  userChoice,
  isPlaying,
}) => {
  const [activeText, setActiveText] = useState<string | null>(null);
  const [activeUtopiaText, setActiveUtopiaText] = useState<string | null>(null);
  const [activeDystopiaText, setActiveDystopiaText] = useState<string | null>(null);

  useEffect(() => {
    // If playback is paused or there's no section, clear all text and do nothing.
    if (!isPlaying || !currentSection?.timedTextKey) {
      setActiveText(null);
      setActiveUtopiaText(null);
      setActiveDystopiaText(null);
      return;
    }

    // Reset texts at the beginning of each check within a valid section
    setActiveText(null);
    setActiveUtopiaText(null);
    setActiveDystopiaText(null);

    const sectionKey = currentSection.timedTextKey as keyof TimedTextData;
    const sectionData = timedTextData[sectionKey];

    if (!sectionData) return;

    // Logic for the 'Choice Corridor'
    if (sectionKey === 'choiceCorridor') {
      const { utopia, dystopia } = sectionData as TimedTextData['choiceCorridor'];
      if (currentTime >= utopia.on && currentTime < utopia.off) setActiveUtopiaText(utopia.text);
      if (currentTime >= dystopia.on && currentTime < dystopia.off) setActiveDystopiaText(dystopia.text);

    // Logic for the final 'Utopia' or 'Dystopia' sections
    } else if (sectionKey === 'branchingFutures') {
      if (userChoice !== 'none') {
        const futureCard = (sectionData as TimedTextData['branchingFutures'])[userChoice];
        if (futureCard && currentTime >= futureCard.on && currentTime < futureCard.off) {
          setActiveText(futureCard.text);
        } else if (currentTime >= 540 && currentTime < 555) {
          // Show transition text during the gap between choice and branching futures
          setActiveText(`Entering the ${userChoice === 'utopia' ? 'Utopian' : 'Dystopian'} future...`);
        }
      }

    // Logic for all other sections with timed text cards
    } else if (Array.isArray(sectionData)) {
      const currentCard = sectionData.find(
        (card) => currentTime >= card.on && currentTime < card.off
      );
      if (currentCard) {
        setActiveText(currentCard.text);
      }
    }
  }, [currentTime, currentSection, userChoice, isPlaying]);

  const textVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
  };

  // Changed to flex-col to stack era name and timed text, and centered vertically
  const containerStyles = "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-4xl mx-auto p-4 flex flex-col justify-center items-center";

  if (currentSection?.timedTextKey === 'choiceCorridor') {
    return (
      <div className="text-center text-white w-full max-w-4xl mx-auto flex flex-col items-center justify-center h-full">
        <AnimatePresence mode="wait">
          {isPlaying && (activeUtopiaText || activeDystopiaText) && (
            <div className={`${containerStyles} flex-row justify-between`}>
              <AnimatePresence>
                {activeUtopiaText && (
                  <motion.div
                    key="utopia-text"
                    variants={textVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ duration: 0.8, ease: 'easeInOut' }}
                    className="w-1/2 text-center text-lg md:text-xl font-light text-white/80 p-4"
                  >
                    {activeUtopiaText}
                  </motion.div>
                )}
              </AnimatePresence>
              <AnimatePresence>
                {activeDystopiaText && (
                  <motion.div
                    key="dystopia-text"
                    variants={textVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ duration: 0.8, ease: 'easeInOut' }}
                    className="w-1/2 text-center text-lg md:text-xl font-light text-white/80 p-4"
                  >
                    {activeDystopiaText}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className={containerStyles}>
      <AnimatePresence mode="wait">
        {isPlaying && currentSection && (
          <motion.h2
            key={`section-${currentSection.id}`} // Animate when section changes
            variants={textVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.8, ease: 'easeInOut' }}
            className="text-center text-4xl md:text-5xl font-bold text-white mb-4"
          >
            {currentSection.section}
          </motion.h2>
        )}
      </AnimatePresence>
      <AnimatePresence mode="wait">
        {isPlaying && activeText && (
          <motion.p
            key={activeText} // Keying by text content ensures re-render on change
            variants={textVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.8, ease: 'easeInOut' }}
            className="text-center text-2xl md:text-3xl font-light text-white/80"
          >
            {activeText}
          </motion.p>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TimedTextDisplay;
