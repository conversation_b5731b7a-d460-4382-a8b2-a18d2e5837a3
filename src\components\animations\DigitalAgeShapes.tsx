'use client';

import React, { useMemo } from 'react';
import { random } from '@/lib/random';
import styles from './DigitalAgeShapes.module.css';

interface DigitalAgeShapesProps {
  progress: number;
}

interface Point {
  id: number;
  x: number;
  y: number;
  dx: number;
  dy: number;
  duration: number;
}

const DigitalAgeShapes: React.FC<DigitalAgeShapesProps> = ({ progress }) => {
  const pointCount = 60;
  const connectionDistance = 20; // In viewBox units

  const points = useMemo<Point[]>(() => {
    return Array.from({ length: pointCount }).map((_, i) => ({
      id: i,
      x: random(5, 95),
      y: random(5, 95),
      dx: random(-15, 15),
      dy: random(-15, 15),
      duration: random(15, 30),
    }));
  }, []);

  const lines = useMemo(() => {
    const lines = [];
    for (let i = 0; i < points.length; i++) {
      for (let j = i + 1; j < points.length; j++) {
        const p1 = points[i];
        const p2 = points[j];
        const dist = Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));

        if (dist < connectionDistance) {
          lines.push({
            id: `${i}-${j}`,
            p1: p1.id,
            p2: p2.id,
            x1: p1.x,
            y1: p1.y,
            x2: p2.x,
            y2: p2.y,
          });
        }
      }
    }
    return lines;
  }, [points]);

  const visiblePoints = useMemo(() => {
    const count = Math.floor(progress * points.length);
    return points.slice(0, count);
  }, [progress, points]);

  const visiblePointIds = useMemo(() => new Set(visiblePoints.map(p => p.id)), [visiblePoints]);

  const visibleLines = useMemo(() => {
    return lines.filter(line => {
      return visiblePointIds.has(line.p1) && visiblePointIds.has(line.p2);
    });
  }, [visiblePointIds, lines]);


  return (
    <div className={styles.container}>
      <svg className={styles.plexus} viewBox="0 0 100 100" preserveAspectRatio="xMidYMid slice">
        {/* Group for lines */}
        <g>
          {visibleLines.map(line => (
            <line
              key={line.id}
              x1={line.x1}
              y1={line.y1}
              x2={line.x2}
              y2={line.y2}
              className={styles.line}
            />
          ))}
        </g>
        {/* Group for nodes */}
        <g>
          {visiblePoints.map(point => (
            <circle
              key={point.id}
              cx={point.x}
              cy={point.y}
              r="0.4"
              className={styles.node}
              style={{
                '--dx': point.dx,
                '--dy': point.dy,
                '--move-duration': `${point.duration}s`,
              } as React.CSSProperties}
            />
          ))}
        </g>
      </svg>
    </div>
  );
};

export default DigitalAgeShapes;
