# Interactive Soundscape: Codebase Analysis

**Version:** 1.0
**Date:** 2025-06-27

This document provides a comprehensive analysis of the Interactive Soundscape application codebase. It is intended to serve as a detailed guide for developers, outlining the system's architecture, core functionalities, and data structures. It also identifies potential areas for improvement and further development.

---

## 1. High-Level Overview

### 1.1. Project Purpose

The Interactive Soundscape is a web-based narrative experience that guides users through a 10-minute audio-visual journey spanning Earth's 4-billion-year history. It aims to deliver an immersive and educational story, culminating in a user-driven choice that determines one of two possible futures.

### 1.2. Core Concepts

-   **Timeline Progression:** The experience is structured around a fixed 10-minute timeline, divided into distinct geological and historical eras. Each era has unique visuals, sounds, and narrative text.
-   **Audio-Visual Synchronization:** The core of the experience is the tight synchronization between a continuous audio track and evolving visual animations. The application's state is primarily driven by the `currentTime` of the audio player.
-   **User Choice:** At a pivotal moment (the "Choice Corridor"), the user makes a choice between "Utopia" and "Dystopia." This choice alters the final chapter of the experience, including the audio track and visuals.
-   **Collective Results:** User choices are anonymously aggregated and displayed on a results page, showing the collective leaning of all participants who have completed the experience.

### 1.3. Technology Stack

-   **Framework:** Next.js 15 (with Turbopack)
-   **Language:** TypeScript
-   **UI & Styling:**
    -   React 19
    -   Tailwind CSS
    -   `shadcn/ui` for core UI components (Button, Dialog)
    -   `Framer Motion` for animations
-   **State Management:** React Hooks (`useState`, `useEffect`, `useRef`, custom hooks)
-   **Backend:** Next.js API Routes
-   **Data Persistence:** Vercel KV (for production vote counting)
-   **Testing:**
    -   **Unit/Integration:** Jest & React Testing Library
    -   **E2E:** Playwright
    -   **Accessibility:** `@axe-core/playwright`
-   **Linting & Formatting:** ESLint

---

## 2. System Architecture

The application follows a standard Next.js App Router structure, separating concerns between pages, components, hooks, and static data.

### 2.1. Frontend Architecture (Next.js)

-   **Directory Structure:**
    -   `src/app/`: Contains the main pages (`/`, `/experience`, `/results`), API routes, and global styles. It also includes several test pages for isolated component testing (e.g., `/test-fire`, `/test-seamless-audio`).
    -   `src/components/`: Houses all reusable React components. This is further subdivided into `ui` (from shadcn), and `animations` (for the era-specific visual components).
    -   `src/hooks/`: Contains custom hooks that encapsulate complex, reusable logic, most notably the audio players.
    -   `src/data/`: Stores static JSON files that drive the experience's structure, timing, and text content.
    -   `src/lib/`: Utility functions (`cn` for classnames, `random`).
    -   `src/types/`: TypeScript type definitions.
    -   `public/`: Static assets, including the core audio files (`.mp3`) and SVG icons.

-   **Page Routing:**
    -   `/`: The landing page (`src/app/page.tsx`).
    -   `/experience`: The main interactive journey (`src/app/experience/page.tsx`).
    -   `/results`: The page displaying aggregated user choices (`src/app/results/page.tsx`).

-   **Component-Based Structure:** The UI is built from a hierarchy of React components. The `ExperiencePage` acts as the main orchestrator, composing various smaller components like `Timeline`, `TimedTextDisplay`, and `EvolvingBackground` to build the complete user experience.

### 2.2. Backend Architecture (Next.js API Routes)

The backend is lean, consisting of two serverless functions responsible for the voting mechanism.

-   **API Endpoints:**
    -   `POST /api/vote`: Receives a user's choice ('utopia' or 'dystopia'). It increments the corresponding counter in the database.
    -   `GET /api/votes`: Retrieves the current aggregated vote counts for 'utopia' and 'dystopia'.

-   **Data Persistence:**
    -   **Production:** Uses Vercel KV, a serverless Redis database, for durable and scalable vote counting. The connection is managed via environment variables.
    -   **Development:** Falls back to a simple in-memory JavaScript object (`mockVotes`). This allows for easy local testing but means vote counts are reset on every server restart.

---

## 3. Core System Deep Dives

### 3.1. The Experience Page (`/experience`)

This is the most complex part of the application, acting as the central controller for the entire journey.

-   **State Management:** The page heavily relies on `useState` and `useEffect`. The primary driver is the `currentTime` from the audio player. Multiple `useEffect` hooks listen for changes in `currentTime` to:
    1.  Determine the current `Section` and `Era` based on `timeline.json`.
    2.  Show or hide the `ChoiceCorridor` component.
    3.  Trigger the preloading of the final audio tracks.
    4.  Handle the automatic choice if the user doesn't select one in time.
    5.  Redirect to the `/results` page when the audio finishes.

-   **Audio-Visual Synchronization:** The `currentEra` state variable is passed down to `EvolvingBackground`, which then renders the appropriate background colors and `AnimatedShapes` for that era. This ensures the visuals are always in sync with the audio's progress.

-   **The Choice Mechanism:**
    -   Between `CHOICE_START_TIME` (480s) and `CHOICE_END_TIME` (540s), the `ChoiceCorridor` component is displayed.
    -   The `useDualTrackAudioPlayer` starts preloading the Utopia and Dystopia audio tracks in the background.
    -   When a user clicks a choice, the `switchToTrack` function is called for a seamless audio transition. The `userChoice` and `choiceMade` state variables are updated to reflect the selection and prevent further choices.
    -   An asynchronous `fetch` call is made to the `/api/vote` endpoint.
    -   If no choice is made, a `useEffect` hook triggers a random selection after `CHOICE_END_TIME`.

### 3.2. Audio Playback System

The application uses two custom hooks for audio management.

-   **`useAudioPlayer` Hook:** A general-purpose audio player hook that encapsulates a single `<audio>` element. It manages state for `isPlaying`, `currentTime`, `duration`, etc. It includes a "one-shot" playback feature where `pause` is disabled after playback begins, which is ideal for the main experience.

-   **`useDualTrackAudioPlayer` Hook:** This is a more specialized and critical hook built for the choice moment.
    -   It manages three audio elements: a primary one for the main track, and two hidden ones for preloading the Utopia and Dystopia tracks.
    -   The `preloadTracks` function loads the alternate tracks into the hidden audio elements and seeks them to the primary track's current time.
    -   The `switchToTrack` function performs the seamless switch: it pauses the primary audio, instantly replaces the primary `audioRef` with the preloaded one, and calls `play()`. This avoids any network latency or buffering, making the transition imperceptible.

### 3.3. Visual & Animation System

The visual system is designed to be modular and data-driven.

-   **`EvolvingBackground` & `eraVisuals.json`:** `EvolvingBackground` is the top-level visual controller. It reads the `currentEra` and looks up the corresponding visual data in `eraVisuals.json`. This JSON file defines background colors, transition durations, and fade timings for each era, allowing for easy visual tuning without code changes.

-   **`AnimatedShapes` & Individual Era Animations:** `EvolvingBackground` renders the `AnimatedShapes` component, which acts as a router for the actual animations. Based on the `currentEra`, it mounts the specific animation component (e.g., `PrimordialSoupShapes.tsx`, `AgeOfReptilesShapes.tsx`). These components are self-contained and responsible for their own animation logic, often driven by a `progress` prop (a value from 0 to 1 representing progress through the era).

### 3.4. Timeline and Data

The entire experience is driven by a set of well-structured JSON files.

-   **`timeline.json`:** This is the master data file. It defines the sequence of all sections in the experience, their start and end times (in seconds), and a `timedTextKey` which links to the narrative content.
-   **`timedText.json`:** This file contains all the narrative text displayed on screen. It's organized by the `timedTextKey` from `timeline.json`, with each piece of text having an `on` and `off` time.
-   **`Timeline` Component:** This component visualizes the data from `timeline.json`. It calculates the relative width of each section to build the progress bar and uses the `currentTime` to position the "NOW" marker.

---

## 4. File-by-File Breakdown

This section provides a granular description of each relevant file in the codebase.

<details>
<summary><strong>`src/app/` - Pages and Layouts</strong></summary>

-   **`layout.tsx`**: The root layout for the entire application. It imports global CSS and sets up the `Geist` font.
-   **`globals.css`**: Global stylesheet, primarily for Tailwind CSS setup and custom CSS variables for theming.
-   **`page.tsx`**: The landing page component. It presents the introduction and a button to start the experience, which opens the `HeadphoneDialog`.
-   **`experience/page.tsx`**: The main experience orchestrator. Manages audio, state, and renders all core components (`Timeline`, `EvolvingBackground`, etc.). This is the most complex component.
-   **`results/page.tsx`**: The results page. It fetches vote data from `/api/votes` and displays the percentages in a bar chart format.
-   **`api/vote/route.ts`**: Backend API route to handle `POST` requests for submitting a vote.
-   **`api/votes/route.ts`**: Backend API route to handle `GET` requests for fetching vote totals.
-   **`debug-audio/page.tsx`**: A test page for debugging the `useAudioPlayer` hook in isolation.
-   **`test-*/**/page.tsx`**: Various pages for testing specific animation components or features in isolation.

</details>

<details>
<summary><strong>`src/components/` - Core & UI Components</strong></summary>

-   **`AnimatedShapes.tsx`**: A controller component that receives the current era and renders the appropriate animation component (e.g., `PrimordialSoupShapes`). It manages the cross-fade transitions between eras.
-   **`ChoiceCorridor.tsx`**: The modal-like component that appears during the choice window, presenting the "Utopia" and "Dystopia" buttons.
-   **`CountdownTimer.tsx`**: Displays the remaining time of the experience, formatted from seconds into a `M:SS` string.
-   **`EvolvingBackground.tsx`**: Manages the animated background gradients based on `eraVisuals.json` and renders `AnimatedShapes`.
-   **`FullscreenToggle.tsx`**: A UI control that allows the user to enter and exit fullscreen mode.
-   **`HeadphoneDialog.tsx`**: A dialog shown on the landing page recommending the use of headphones.
-   **`TimedTextDisplay.tsx`**: Displays the narrative text and section titles, synchronized with the audio `currentTime` and data from `timedText.json`.
-   **`Timeline.tsx`**: Renders the interactive timeline at the bottom of the experience page.
-   **`VolumeControl.tsx`**: A UI control for adjusting the audio volume.
-   **`ui/`**: Contains `shadcn/ui` components like `button.tsx` and `dialog.tsx`.
-   **`animations/`**: Contains a separate `.tsx` file for each era's unique visual animation (e.g., `CambrianBurstShapes.tsx`, `IndustrialPulseShapes.tsx`).

</details>

<details>
<summary><strong>`src/hooks/` - Custom Hooks</strong></summary>

-   **`useAudioPlayer.ts`**: A hook for managing a single HTML5 audio element. Provides controls for play, pause, seek, and volume, along with state tracking.
-   **`useDualTrackAudioPlayer.ts`**: A specialized hook for managing seamless audio transitions. It internally uses three audio elements to preload tracks and switch them without interruption. This is critical for the user choice moment.

</details>

<details>
<summary><strong>`src/data/` - Data Files</strong></summary>

-   **`timeline.json`**: An array of `Section` objects that defines the structure and timing of the entire 10-minute experience.
-   **`timedText.json`**: A JSON object containing all the narrative text, keyed by section, with specific `on` and `off` timestamps.
-   **`eraVisuals.json`**: A JSON object defining the visual style (background colors, transition speeds) for each era.

</details>

<details>
<summary><strong>`tests/` - Testing Files</strong></summary>

-   **`src/__tests__/`**: Contains Jest/RTL unit and integration tests for components and hooks.
-   **`tests/e2e/`**: Contains Playwright E2E tests that simulate user flows, such as navigating the app, making a choice, and checking for accessibility violations.

</details>

---

## 5. Data Schemas

### 5.1. `timeline.json`

This file is an array of `Section` objects.

```typescript
interface Section {
  id: number;
  start: number; // Start time in seconds
  end: number;   // End time in seconds
  section: string; // Name of the section/era
  timedTextKey: string; // Key to look up text in timedText.json
  // ... other descriptive fields
}
```

### 5.2. `timedText.json`

This file is an object where keys correspond to `timedTextKey` from the timeline.

```typescript
interface TimedTextCard {
  on: number;  // Time in seconds to show the text
  off: number; // Time in seconds to hide the text
  text: string;
}

type TimedTextData = {
  [key: string]: TimedTextCard[] | { utopia: TimedTextCard; dystopia: TimedTextCard };
};
```

### 5.3. `eraVisuals.json`

This file defines the visual properties for each era.

```typescript
interface EraVisual {
  colors: string[]; // Array of 3 colors for the background gradient
  fadeInDuration: number;
  fadeOutDuration: number;
  stageDuration?: number; // For multi-stage eras
}

type EraVisualsData = {
  [key: string]: EraVisual | EraVisual[];
};
```

---

## 6. Identified Issues & Recommendations

This section highlights potential areas for improvement, refactoring, and hardening.

### 6.1. State Management Complexity

-   **Issue:** The main `ExperiencePage` component has a significant amount of logic within `useEffect` hooks that are all dependent on `currentTime`. This can make the component's behavior difficult to trace and prone to race conditions or infinite loops if not managed carefully.
-   **Recommendation:** For future development or refactoring, consider introducing a state machine library (like **XState**). A state machine would provide a much more robust and predictable way to manage the application's flow (e.g., `LOADING` -> `PLAYING_ERA_1` -> `CHOICE_WINDOW` -> `PLAYING_UTOPIA` -> `RESULTS`). This would centralize the transition logic and make it easier to reason about.

### 6.2. Backend & Voting Robustness

-   **Issue:** The development fallback for the voting API uses a simple in-memory object, which resets on server restart. While fine for local dev, it's not a durable solution.
-   **Issue:** The `submitVote` function in `ExperiencePage` does not provide any user feedback if the API call fails. The error is simply logged to the console.
-   **Recommendation:** For a more robust local development environment, consider using a local Redis instance via Docker to mirror the production Vercel KV setup. Additionally, implement user feedback for the voting process (e.g., a subtle toast notification on success or failure).

### 6.3. Randomness of Automatic Choice

-   **Issue:** If a user does not make a choice, the outcome is determined by `Math.random() < 0.5`. Client-side `Math.random()` is not cryptographically secure and can sometimes be predictable or biased depending on the browser's implementation.
-   **Recommendation:** While the stakes are low, for a truly unbiased random choice, this logic could be moved to the backend. An API endpoint could be called to provide a random result, using a more robust server-side random number generator.

### 6.4. Performance Considerations

-   **Issue:** The animation components, particularly those with many particles (`IndustrialPulseShapes`, `EarlyHumansShapes`, `CambrianBurstShapes`), could be performance-intensive on devices with less powerful GPUs or CPUs.
-   **Recommendation:** The current implementation using CSS animations and Framer Motion with DOM elements is clever. However, for scenes with hundreds of particles, rendering to a `<canvas>` element (either via the 2D Canvas API or a WebGL library like **Three.js / react-three-fiber**) would offer significantly better performance by offloading the work to the GPU more efficiently.

### 6.5. Potential Race Condition in Audio Switching

-   **Issue:** The `useDualTrackAudioPlayer`'s `switchToTrack` function relies on `setTimeout` with a small delay (50ms) to ensure the primary audio has paused before the new track starts. This is generally effective but not guaranteed. A slow system could potentially process the pause after the timeout has elapsed.
-   **Recommendation:** A more robust solution would be to listen for the `pause` event on the audio element before proceeding with the switch. This would create an event-driven flow that is more reliable than a fixed-timeout approach.
