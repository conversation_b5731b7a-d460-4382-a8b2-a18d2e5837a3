'use client';

import React from 'react';
import FullscreenToggle from '@/components/FullscreenToggle';
import { But<PERSON> } from '@/components/ui/button';
import { Play, Volume2 } from 'lucide-react';

const TestFullscreenPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex flex-col items-center justify-between p-12 text-white">
      <div className="flex-grow flex flex-col items-center justify-center">
        <h1 className="text-4xl font-bold mb-8">Fullscreen Toggle Test</h1>
        <p className="text-lg text-center max-w-2xl mb-8">
          This page demonstrates the fullscreen toggle button integration. 
          The button should appear in the bottom right corner with the same styling as other UI controls.
        </p>
        <div className="bg-black/30 rounded-xl p-6 backdrop-blur-lg border border-white/10">
          <h2 className="text-xl font-semibold mb-4">Test Instructions:</h2>
          <ol className="space-y-2 text-sm">
            <li><strong>1.</strong> Look for the fullscreen toggle button in the bottom right corner</li>
            <li><strong>2.</strong> Click it to enter fullscreen mode</li>
            <li><strong>3.</strong> Verify the icon changes to a minimize icon</li>
            <li><strong>4.</strong> Click again to exit fullscreen mode</li>
            <li><strong>5.</strong> Verify all controls remain functional in both modes</li>
          </ol>
        </div>
      </div>

      {/* Footer mimicking the experience page layout */}
      <footer className="w-full flex flex-col items-center gap-6 z-20">
        <div className="w-full px-4">
          {/* Mock timeline */}
          <div className="w-full h-2 bg-gray-700/50 rounded-full">
            <div className="h-full w-1/3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
          </div>
        </div>
        
        <div className="relative w-full flex justify-center">
          <div className="flex items-center gap-4">
            {/* Mock Play button */}
            <div className="rounded-md p-px bg-gradient-to-b from-gray-300/25 to-gray-600/20">
              <Button
                size="lg"
                className="bg-black/50 backdrop-blur-sm border-0 hover:bg-white/10 rounded-[6px] h-10"
              >
                <Play className="mr-2 h-4 w-4" />
                Play
              </Button>
            </div>
            
            {/* Mock Volume Control */}
            <div className="rounded-md p-px bg-gradient-to-b from-gray-300/25 to-gray-600/20">
              <div className="flex items-center bg-black/50 backdrop-blur-sm rounded-[6px] h-10 transition-all duration-300 ease-in-out hover:bg-white/10">
                <button className="h-full px-3 flex items-center bg-transparent rounded-l-[6px] rounded-r-none transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ring-offset-background">
                  <Volume2 className="h-5 w-5" />
                </button>
                <div className="w-24 pr-4 flex items-center">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.01"
                    defaultValue="0.7"
                    className="w-full h-2 bg-gray-700/50 rounded-lg appearance-none cursor-pointer slider-thumb"
                  />
                </div>
              </div>
            </div>
          </div>
          
          {/* Fullscreen toggle positioned in bottom right */}
          <div className="absolute right-0 top-0">
            <FullscreenToggle />
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TestFullscreenPage;
