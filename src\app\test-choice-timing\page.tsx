'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import ChoiceCorridor from '@/components/ChoiceCorridor';

const TestChoiceTimingPage: React.FC = () => {
  const [showChoice, setShowChoice] = useState(false);
  const [choiceMade, setChoiceMade] = useState(false);
  const [userChoice, setUserChoice] = useState<'utopia' | 'dystopia' | 'none'>('none');
  const [timeElapsed, setTimeElapsed] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 0.1);
    }, 100);

    return () => clearInterval(interval);
  }, []);

  const handleStartTest = () => {
    setShowChoice(true);
    setChoiceMade(false);
    setUserChoice('none');
    setTimeElapsed(0);
  };

  const handleSelectUtopia = () => {
    setUserChoice('utopia');
    setChoiceMade(true);
    setShowChoice(false);
  };

  const handleSelectDystopia = () => {
    setUserChoice('dystopia');
    setChoiceMade(true);
    setShowChoice(false);
  };

  const handleReset = () => {
    setShowChoice(false);
    setChoiceMade(false);
    setUserChoice('none');
    setTimeElapsed(0);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex flex-col items-center justify-center p-8">
      <div className="max-w-4xl w-full text-center text-white">
        <h1 className="text-4xl font-bold mb-8">Choice Corridor Timing Test</h1>
        
        <div className="mb-8 p-6 bg-black/30 rounded-xl backdrop-blur-lg border border-white/10">
          <h2 className="text-2xl font-semibold mb-4">Test Results</h2>
          <div className="grid grid-cols-2 gap-4 text-lg">
            <div>
              <strong>Time Elapsed:</strong> {timeElapsed.toFixed(1)}s
            </div>
            <div>
              <strong>Choice Made:</strong> {choiceMade ? userChoice : 'None'}
            </div>
            <div>
              <strong>Modal Visible:</strong> {showChoice ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>Expected Delay:</strong> 3 seconds
            </div>
          </div>
        </div>

        <div className="space-x-4 mb-8">
          <Button 
            onClick={handleStartTest}
            disabled={showChoice}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg"
          >
            Start Test
          </Button>
          <Button 
            onClick={handleReset}
            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg"
          >
            Reset
          </Button>
        </div>

        <div className="text-sm text-gray-300 mb-8">
          <p>Click "Start Test" to see the choice modal with a 3-second delay.</p>
          <p>The modal should appear after exactly 3 seconds without any background overlay changes.</p>
        </div>

        {/* Test Area */}
        <div className="relative h-96 bg-gradient-to-br from-white via-black to-gray-400 rounded-xl overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-2xl font-bold text-white">
              Choice Corridor Background Test Area
            </div>
          </div>

          <AnimatePresence>
            {showChoice && (
              <ChoiceCorridor
                onSelectUtopia={handleSelectUtopia}
                onSelectDystopia={handleSelectDystopia}
              />
            )}
          </AnimatePresence>
        </div>

        {choiceMade && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8 p-4 bg-green-600/20 border border-green-400/30 rounded-lg"
          >
            <h3 className="text-xl font-semibold text-green-400">
              Choice Made: {userChoice === 'utopia' ? '🌟 Utopia' : '🔥 Dystopia'}
            </h3>
            <p className="text-green-300">
              Time to choice: {timeElapsed.toFixed(1)} seconds
            </p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default TestChoiceTimingPage;
