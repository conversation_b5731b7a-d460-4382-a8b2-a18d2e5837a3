import { NextResponse } from 'next/server';
import { isProductionEnvironment, getMockVotes } from '@/lib/mockVoteStorage';

export async function GET() {
  try {
    let utopiaVotes = 0;
    let dystopiaVotes = 0;

    if (isProductionEnvironment()) {
      // Use Vercel KV in production
      const { kv } = await import('@vercel/kv');
      const [utopiaResult, dystopiaResult] = await Promise.all([
        kv.get('soundscape:votes:utopia'),
        kv.get('soundscape:votes:dystopia'),
      ]);
      utopiaVotes = utopiaResult || 0;
      dystopiaVotes = dystopiaResult || 0;
    } else {
      // Use mock storage in development
      const mockVotes = getMockVotes();
      utopiaVotes = mockVotes.utopia;
      dystopiaVotes = mockVotes.dystopia;
      console.log(`[DEV MODE] Mock vote counts:`, mockVotes);
    }

    return NextResponse.json({
      utopia: utopiaVotes,
      dystopia: dystopiaVotes,
    }, { status: 200 });

  } catch (error) {
    console.error('Error fetching vote counts:', error);
    return NextResponse.json({ message: 'An error occurred while fetching vote counts.' }, { status: 500 });
  }
}
