'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface UseDualTrackAudioPlayerProps {
  initialSrc: string;
  utopiaTrack?: string;
  dystopiaTrack?: string;
}

interface DualTrackAudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  hasStarted: boolean;
  remainingTime: number;
  activeTrack: string;
  isPreloading: boolean;
  preloadProgress: { utopia: boolean; dystopia: boolean };
  play: () => void;
  pause: () => void;
  changeSource: (newSrc: string, playWhenReady?: boolean) => void;
  switchToTrack: (track: 'utopia' | 'dystopia') => Promise<boolean>;
  preloadTracks: (utopiaTrack: string, dystopiaTrack: string) => Promise<void>;
  seek: (time: number, duration?: number) => void;
  setVolume: (volume: number) => void;
  reset: () => void;
  cleanup: () => void;
}

export const useDualTrackAudioPlayer = ({
  initialSrc,
  utopiaTrack,
  dystopiaTrack
}: UseDualTrackAudioPlayerProps): DualTrackAudioPlayerState => {
  const primaryAudioRef = useRef<HTMLAudioElement | null>(null);
  const utopiaAudioRef = useRef<HTMLAudioElement | null>(null);
  const dystopiaAudioRef = useRef<HTMLAudioElement | null>(null);

  // Store event listener references for proper cleanup
  const eventListenersRef = useRef<{
    setAudioData?: () => void;
    setAudioTime?: () => void;
    handlePlay?: () => void;
    handlePause?: () => void;
  }>({});

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolumeState] = useState(1);
  const [hasStarted, setHasStarted] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const [activeTrack, setActiveTrack] = useState(initialSrc);
  const [isPreloading, setIsPreloading] = useState(false);
  const [preloadProgress, setPreloadProgress] = useState({ utopia: false, dystopia: false });

  // Initialize primary audio player
  useEffect(() => {
    if (typeof window !== 'undefined' && !primaryAudioRef.current) {
      primaryAudioRef.current = new Audio(initialSrc);

      // Create event listeners and store references
      const setAudioData = () => {
        setDuration(primaryAudioRef.current?.duration || 0);
        setCurrentTime(primaryAudioRef.current?.currentTime || 0);
      };

      const setAudioTime = () => setCurrentTime(primaryAudioRef.current?.currentTime || 0);
      const handlePlay = () => setIsPlaying(true);
      const handlePause = () => setIsPlaying(false);

      // Store references for cleanup
      eventListenersRef.current = {
        setAudioData,
        setAudioTime,
        handlePlay,
        handlePause
      };

      primaryAudioRef.current.addEventListener('loadeddata', setAudioData);
      primaryAudioRef.current.addEventListener('timeupdate', setAudioTime);
      primaryAudioRef.current.addEventListener('play', handlePlay);
      primaryAudioRef.current.addEventListener('pause', handlePause);

      return () => {
        if (primaryAudioRef.current && eventListenersRef.current) {
          primaryAudioRef.current.removeEventListener('loadeddata', eventListenersRef.current.setAudioData!);
          primaryAudioRef.current.removeEventListener('timeupdate', eventListenersRef.current.setAudioTime!);
          primaryAudioRef.current.removeEventListener('play', eventListenersRef.current.handlePlay!);
          primaryAudioRef.current.removeEventListener('pause', eventListenersRef.current.handlePause!);
          primaryAudioRef.current.pause();
          primaryAudioRef.current = null;
        }
      };
    }
  }, [initialSrc]);

  // Sync volume across all audio elements
  useEffect(() => {
    if (primaryAudioRef.current) {
      primaryAudioRef.current.volume = volume;
    }
    if (utopiaAudioRef.current) {
      utopiaAudioRef.current.volume = volume;
    }
    if (dystopiaAudioRef.current) {
      dystopiaAudioRef.current.volume = volume;
    }
  }, [volume]);

  // Calculate remaining time
  useEffect(() => {
    if (duration > 0) {
      const remaining = Math.max(0, duration - currentTime);
      setRemainingTime(remaining);
    }
  }, [currentTime, duration]);

  const play = useCallback(() => {
    if (primaryAudioRef.current) {
      primaryAudioRef.current.play();
      if (!hasStarted) {
        setHasStarted(true);
      }
    }
  }, [hasStarted]);

  const pause = useCallback(() => {
    if (!hasStarted && primaryAudioRef.current) {
      primaryAudioRef.current.pause();
    }
  }, [hasStarted]);

  // Traditional source change (for non-choice scenarios) - preserves current time
  const changeSource = useCallback((newSrc: string, playWhenReady = true) => {
    if (primaryAudioRef.current) {
      const time = primaryAudioRef.current.currentTime;
      const wasPlaying = !primaryAudioRef.current.paused;

      console.log(`[DualTrackAudioPlayer] Traditional source change: ${primaryAudioRef.current.src} -> ${newSrc} at ${time}s`);

      primaryAudioRef.current.pause();

      setTimeout(() => {
        if (primaryAudioRef.current) {
          const cacheBustingSrc = newSrc + (newSrc.includes('?') ? '&' : '?') + 't=' + Date.now();
          primaryAudioRef.current.src = cacheBustingSrc;
          primaryAudioRef.current.load();

          const onCanPlay = () => {
            if (primaryAudioRef.current) {
              // Preserve the current time instead of resetting to 0
              primaryAudioRef.current.currentTime = time;
              setCurrentTime(time); // Update state immediately
              if (playWhenReady && wasPlaying) {
                primaryAudioRef.current.play().catch(console.error);
              }
            }
            primaryAudioRef.current?.removeEventListener('canplay', onCanPlay);
          };

          primaryAudioRef.current.addEventListener('canplay', onCanPlay);
        }
      }, 50);
    }
    setActiveTrack(newSrc);
  }, []);

  // Pre-load both tracks for seamless switching
  const preloadTracks = useCallback(async (utopiaTrack: string, dystopiaTrack: string): Promise<void> => {
    console.log('[DualTrackAudioPlayer] Starting track preloading...');
    setIsPreloading(true);
    setPreloadProgress({ utopia: false, dystopia: false });

    const currentTime = primaryAudioRef.current?.currentTime || 0;

    try {
      // Create and preload utopia track
      const utopiaPromise = new Promise<void>((resolve, reject) => {
        if (utopiaAudioRef.current) {
          utopiaAudioRef.current.pause();
          utopiaAudioRef.current = null;
        }

        utopiaAudioRef.current = new Audio(utopiaTrack);
        utopiaAudioRef.current.volume = volume;
        utopiaAudioRef.current.preload = 'auto';

        const onUtopiaLoaded = () => {
          if (utopiaAudioRef.current) {
            utopiaAudioRef.current.currentTime = currentTime;
            setPreloadProgress(prev => ({ ...prev, utopia: true }));
            console.log('[DualTrackAudioPlayer] Utopia track preloaded');
            resolve();
          }
        };

        const onUtopiaError = () => {
          console.error('[DualTrackAudioPlayer] Failed to preload utopia track');
          reject(new Error('Failed to preload utopia track'));
        };

        utopiaAudioRef.current.addEventListener('canplaythrough', onUtopiaLoaded, { once: true });
        utopiaAudioRef.current.addEventListener('error', onUtopiaError, { once: true });
        utopiaAudioRef.current.load();
      });

      // Create and preload dystopia track
      const dystopiaPromise = new Promise<void>((resolve, reject) => {
        if (dystopiaAudioRef.current) {
          dystopiaAudioRef.current.pause();
          dystopiaAudioRef.current = null;
        }

        dystopiaAudioRef.current = new Audio(dystopiaTrack);
        dystopiaAudioRef.current.volume = volume;
        dystopiaAudioRef.current.preload = 'auto';

        const onDystopiaLoaded = () => {
          if (dystopiaAudioRef.current) {
            dystopiaAudioRef.current.currentTime = currentTime;
            setPreloadProgress(prev => ({ ...prev, dystopia: true }));
            console.log('[DualTrackAudioPlayer] Dystopia track preloaded');
            resolve();
          }
        };

        const onDystopiaError = () => {
          console.error('[DualTrackAudioPlayer] Failed to preload dystopia track');
          reject(new Error('Failed to preload dystopia track'));
        };

        dystopiaAudioRef.current.addEventListener('canplaythrough', onDystopiaLoaded, { once: true });
        dystopiaAudioRef.current.addEventListener('error', onDystopiaError, { once: true });
        dystopiaAudioRef.current.load();
      });

      await Promise.all([utopiaPromise, dystopiaPromise]);
      console.log('[DualTrackAudioPlayer] All tracks preloaded successfully');
    } catch (error) {
      console.error('[DualTrackAudioPlayer] Preloading failed:', error);
    } finally {
      setIsPreloading(false);
    }
  }, [volume]);

  // Seamless track switching with proper event listener management
  const switchToTrack = useCallback(async (track: 'utopia' | 'dystopia') => {
    const targetAudioRef = track === 'utopia' ? utopiaAudioRef : dystopiaAudioRef;
    const targetSrc = track === 'utopia' ? utopiaTrack : dystopiaTrack;

    if (!targetAudioRef.current || !targetSrc) {
      console.error(`[DualTrackAudioPlayer] Cannot switch to ${track}: track not preloaded`);
      return false;
    }

    if (!primaryAudioRef.current) {
      console.error('[DualTrackAudioPlayer] Primary audio not available');
      return false;
    }

    try {
      const currentTime = primaryAudioRef.current.currentTime;
      const wasPlaying = !primaryAudioRef.current.paused;

      console.log(`[DualTrackAudioPlayer] Seamless switch to ${track} at ${currentTime}s`);

      // Remove event listeners from old primary
      const oldPrimary = primaryAudioRef.current;
      if (eventListenersRef.current) {
        oldPrimary.removeEventListener('loadeddata', eventListenersRef.current.setAudioData!);
        oldPrimary.removeEventListener('timeupdate', eventListenersRef.current.setAudioTime!);
        oldPrimary.removeEventListener('play', eventListenersRef.current.handlePlay!);
        oldPrimary.removeEventListener('pause', eventListenersRef.current.handlePause!);
      }

      // Pause current track
      if (wasPlaying) {
        oldPrimary.pause();
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Sync time on target track
      targetAudioRef.current.currentTime = currentTime;

      // Replace primary audio element with the target
      primaryAudioRef.current = targetAudioRef.current;

      // Set up event listeners on new primary using stored references
      if (eventListenersRef.current) {
        primaryAudioRef.current.addEventListener('loadeddata', eventListenersRef.current.setAudioData!);
        primaryAudioRef.current.addEventListener('timeupdate', eventListenersRef.current.setAudioTime!);
        primaryAudioRef.current.addEventListener('play', eventListenersRef.current.handlePlay!);
        primaryAudioRef.current.addEventListener('pause', eventListenersRef.current.handlePause!);
      }

      // Update active track
      setActiveTrack(targetSrc);

      // Resume playback if it was playing
      if (wasPlaying) {
        try {
          await primaryAudioRef.current.play();
          console.log(`[DualTrackAudioPlayer] Successfully resumed playback on ${track}`);
        } catch (playError) {
          console.error(`[DualTrackAudioPlayer] Failed to resume playback:`, playError);
          // Try to recover by restarting playback
          setTimeout(() => {
            if (primaryAudioRef.current) {
              primaryAudioRef.current.play().catch(console.error);
            }
          }, 100);
        }
      }

      // Clean up the old primary audio
      oldPrimary.pause();
      oldPrimary.currentTime = 0;

      // Clear the reference to the switched track so it doesn't get cleaned up
      if (track === 'utopia') {
        utopiaAudioRef.current = null;
      } else {
        dystopiaAudioRef.current = null;
      }

      console.log(`[DualTrackAudioPlayer] Successfully switched to ${track}`);
      return true;
    } catch (error) {
      console.error(`[DualTrackAudioPlayer] Error during track switch:`, error);
      return false;
    }
  }, [utopiaTrack, dystopiaTrack]);

  const seek = useCallback((time: number, newDuration?: number) => {
    if (primaryAudioRef.current) {
      primaryAudioRef.current.currentTime = time;
      setCurrentTime(time);
      if (newDuration !== undefined) {
        setDuration(newDuration);
      }
    }

    // Also sync preloaded tracks if they exist
    if (utopiaAudioRef.current && utopiaAudioRef.current !== primaryAudioRef.current) {
      utopiaAudioRef.current.currentTime = time;
    }
    if (dystopiaAudioRef.current && dystopiaAudioRef.current !== primaryAudioRef.current) {
      dystopiaAudioRef.current.currentTime = time;
    }
  }, []);

  const setVolume = useCallback((newVolume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, newVolume));
    setVolumeState(clampedVolume);

    if (primaryAudioRef.current) {
      primaryAudioRef.current.volume = clampedVolume;
    }
    if (utopiaAudioRef.current) {
      utopiaAudioRef.current.volume = clampedVolume;
    }
    if (dystopiaAudioRef.current) {
      dystopiaAudioRef.current.volume = clampedVolume;
    }
  }, []);

  const reset = useCallback(() => {
    if (primaryAudioRef.current) {
      primaryAudioRef.current.pause();
      primaryAudioRef.current.currentTime = 0;
    }
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setHasStarted(false);
    setRemainingTime(0);
    setActiveTrack(initialSrc);
  }, [initialSrc]);

  const cleanup = useCallback(() => {
    console.log('[DualTrackAudioPlayer] Cleaning up preloaded tracks');

    if (utopiaAudioRef.current && utopiaAudioRef.current !== primaryAudioRef.current) {
      utopiaAudioRef.current.pause();
      utopiaAudioRef.current = null;
    }

    if (dystopiaAudioRef.current && dystopiaAudioRef.current !== primaryAudioRef.current) {
      dystopiaAudioRef.current.pause();
      dystopiaAudioRef.current = null;
    }

    setPreloadProgress({ utopia: false, dystopia: false });
    setIsPreloading(false);
  }, []);

  return {
    isPlaying,
    currentTime,
    duration,
    volume,
    hasStarted,
    remainingTime,
    activeTrack,
    isPreloading,
    preloadProgress,
    play,
    pause,
    changeSource,
    switchToTrack,
    preloadTracks,
    seek,
    setVolume,
    reset,
    cleanup
  };
};
