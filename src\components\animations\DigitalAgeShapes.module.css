.container {
  position: absolute;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  opacity: 0.8;
}

.plexus {
  width: 100%;
  height: 100%;
}

.node {
  fill: #ffffff;
  /* Run spawn animation once, then run the move animation infinitely */
  animation: spawn 0.6s ease-out forwards,
             move var(--move-duration) ease-in-out infinite alternate;
  transform-origin: center;
}

.line {
  stroke: rgba(255, 255, 255, 0.25);
  stroke-width: 0.2px; /* Thin lines in the viewBox coordinate system */
  /* Animate the line appearing */
  animation: fadeIn 0.6s ease-out forwards;
}

@keyframes spawn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    stroke-opacity: 0;
  }
  to {
    stroke-opacity: 1;
  }
}

@keyframes move {
  from {
    /* Start moving from the spawned position */
    transform: translate(0, 0) scale(1);
  }
  to {
    /* The move animation is just a translation */
    transform: translate(var(--dx), var(--dy)) scale(1);
  }
}

